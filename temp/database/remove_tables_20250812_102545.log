2025-08-12 10:25:45,583 - INFO - ============================================================
2025-08-12 10:25:45,583 - INFO - contract_risk_analysis 表和视图移除工具
2025-08-12 10:25:45,583 - INFO - ============================================================
2025-08-12 10:25:45,583 - INFO - 🚀 开始移除 contract_risk_analysis 相关表和视图...
2025-08-12 10:25:45,583 - INFO - 📊 检查当前数据库状态...
2025-08-12 10:25:45,986 - ERROR - SQL执行失败: 
            SELECT table_name 
            FROM information_schema.views 
            WHERE table_name = ?
            , 错误: Catalog Error: Table with name views does not exist!
Did you mean "temp.pg_catalog.pg_views"?
LINE 3:             FROM information_schema.views 
                         ^
2025-08-12 10:25:45,986 - ERROR - 检查视图 contract_risk_analysis_view 是否存在时出错: Catalog Error: Table with name views does not exist!
Did you mean "temp.pg_catalog.pg_views"?
LINE 3:             FROM information_schema.views 
                         ^
2025-08-12 10:25:45,986 - INFO - contract_risk_analysis 表存在: True
2025-08-12 10:25:45,986 - INFO - contract_risk_analysis_view 视图存在: False
2025-08-12 10:25:45,986 - INFO - 💾 备份表数据...
2025-08-12 10:25:46,007 - INFO - 表 contract_risk_analysis 为空，无需备份
2025-08-12 10:25:46,007 - INFO - 🗑️ 移除表...
2025-08-12 10:25:46,034 - INFO - ✅ 表 contract_risk_analysis 已成功移除
2025-08-12 10:25:46,034 - INFO - 🗑️ 移除相关序列...
2025-08-12 10:25:46,039 - INFO - ✅ 序列 contract_risk_analysis_id_seq 已成功移除
2025-08-12 10:25:46,039 - INFO - 🔍 验证移除结果...
2025-08-12 10:25:46,065 - ERROR - SQL执行失败: 
            SELECT table_name 
            FROM information_schema.views 
            WHERE table_name = ?
            , 错误: Catalog Error: Table with name views does not exist!
Did you mean "temp.pg_catalog.pg_views"?
LINE 3:             FROM information_schema.views 
                         ^
2025-08-12 10:25:46,065 - ERROR - 检查视图 contract_risk_analysis_view 是否存在时出错: Catalog Error: Table with name views does not exist!
Did you mean "temp.pg_catalog.pg_views"?
LINE 3:             FROM information_schema.views 
                         ^
2025-08-12 10:25:46,065 - INFO - ✅ 验证成功：contract_risk_analysis 表和 contract_risk_analysis_view 视图已完全移除
2025-08-12 10:25:46,066 - INFO - 🎉 contract_risk_analysis 相关表和视图移除完成！
2025-08-12 10:25:46,066 - INFO - ✅ 移除操作成功完成
