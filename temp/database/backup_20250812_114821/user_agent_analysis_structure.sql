-- user_agent_analysis 表结构备份
-- 备份时间: 2025-08-12T11:48:21.352417
-- 表状态: 空表（0行数据）

CREATE TABLE user_agent_analysis(id INTEGER PRIMARY KEY, member_id VARCHAR NOT NULL, analysis_date DATE NOT NULL, total_relationships INTEGER DEFAULT(0), direct_relationships INTEGER DEFAULT(0), indirect_relationships INTEGER DEFAULT(0), relationship_strength DECIMAL(5,4) DEFAULT(0), risk_score DECIMAL(5,4) DEFAULT(0), created_at TIMESTAMP DEFAULT(CURRENT_TIMESTAMP), updated_at TIMESTAMP DEFAULT(CURRENT_TIMESTAMP), analysis_details JSON);;
