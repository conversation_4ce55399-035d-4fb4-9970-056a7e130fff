#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移除 user_agent_analysis 表的脚本
创建时间: 2025-08-12
目的: 清理未使用的预留表
"""

import sys
import os
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../backend'))

from database.duckdb_manager import DuckDBManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'temp/database/remove_user_agent_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)

logger = logging.getLogger(__name__)

class UserAgentAnalysisTableRemover:
    """user_agent_analysis 表移除器"""
    
    def __init__(self):
        self.db_manager = DuckDBManager()
        
    def check_table_exists(self):
        """检查表是否存在"""
        try:
            sql = """
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_name = 'user_agent_analysis'
            """
            result = self.db_manager.execute_sql(sql)
            return len(result) > 0 if result else False
        except Exception as e:
            logger.error(f"检查表是否存在时出错: {e}")
            return False
    
    def get_table_info(self):
        """获取表信息"""
        try:
            # 获取表结构
            columns = self.db_manager.execute_sql('DESCRIBE user_agent_analysis')
            
            # 获取行数
            count_result = self.db_manager.execute_sql('SELECT COUNT(*) as count FROM user_agent_analysis')
            count = count_result[0]['count'] if count_result else 0
            
            return {
                'columns': columns,
                'row_count': count
            }
        except Exception as e:
            logger.error(f"获取表信息时出错: {e}")
            return None
    
    def backup_table_structure(self):
        """备份表结构"""
        try:
            # 创建备份目录
            backup_dir = f"temp/database/backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            os.makedirs(backup_dir, exist_ok=True)
            
            # 获取表创建SQL
            create_sql_result = self.db_manager.execute_sql("""
                SELECT sql 
                FROM sqlite_master 
                WHERE type='table' AND name='user_agent_analysis'
            """)
            
            if create_sql_result:
                backup_file = f"{backup_dir}/user_agent_analysis_structure.sql"
                with open(backup_file, 'w', encoding='utf-8') as f:
                    f.write("-- user_agent_analysis 表结构备份\n")
                    f.write(f"-- 备份时间: {datetime.now().isoformat()}\n")
                    f.write(f"-- 表状态: 空表（0行数据）\n\n")
                    f.write(create_sql_result[0]['sql'] + ";\n")
                
                logger.info(f"✅ 表结构已备份到: {backup_file}")
                return True
            else:
                logger.warning("⚠️ 无法获取表创建SQL")
                return False
                
        except Exception as e:
            logger.error(f"备份表结构时出错: {e}")
            return False
    
    def remove_table(self):
        """移除表"""
        try:
            sql = "DROP TABLE IF EXISTS user_agent_analysis"
            self.db_manager.execute_sql(sql)
            logger.info("✅ user_agent_analysis 表已成功移除")
            return True
            
        except Exception as e:
            logger.error(f"移除表时出错: {e}")
            return False
    
    def verify_removal(self):
        """验证移除结果"""
        try:
            table_exists = self.check_table_exists()
            
            if not table_exists:
                logger.info("✅ 验证成功：user_agent_analysis 表已完全移除")
                return True
            else:
                logger.warning("⚠️ user_agent_analysis 表仍然存在")
                return False
                
        except Exception as e:
            logger.error(f"验证移除结果时出错: {e}")
            return False
    
    def run(self):
        """执行移除操作"""
        logger.info("🚀 开始移除 user_agent_analysis 表...")
        
        try:
            # 1. 检查表是否存在
            logger.info("📊 检查表状态...")
            if not self.check_table_exists():
                logger.info("✅ user_agent_analysis 表不存在，无需移除")
                return True
            
            # 2. 获取表信息
            table_info = self.get_table_info()
            if table_info:
                logger.info(f"📋 表信息: {len(table_info['columns'])} 个字段, {table_info['row_count']} 行数据")
                
                if table_info['row_count'] > 0:
                    logger.warning(f"⚠️ 表中有 {table_info['row_count']} 行数据，请确认是否继续删除")
                    # 在实际环境中，这里可以添加用户确认逻辑
                else:
                    logger.info("✅ 表为空，可以安全删除")
            
            # 3. 备份表结构
            logger.info("💾 备份表结构...")
            if not self.backup_table_structure():
                logger.warning("⚠️ 表结构备份失败，但继续删除操作")
            
            # 4. 移除表
            logger.info("🗑️ 移除表...")
            if not self.remove_table():
                logger.error("❌ 表移除失败")
                return False
            
            # 5. 验证移除结果
            if not self.verify_removal():
                logger.error("❌ 移除验证失败")
                return False
            
            logger.info("🎉 user_agent_analysis 表移除完成！")
            return True
            
        except Exception as e:
            logger.error(f"移除操作失败: {e}")
            return False

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("user_agent_analysis 表移除工具")
    logger.info("=" * 60)
    
    # 创建移除器
    remover = UserAgentAnalysisTableRemover()
    
    # 执行移除
    success = remover.run()
    
    if success:
        logger.info("✅ 移除操作成功完成")
        return 0
    else:
        logger.error("❌ 移除操作失败")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
