-- =============================================
-- 合约风险分析算法结果表结构定义
-- 版本: 2.0 - 拆分存储优化版本
-- =============================================

-- 1. 通用算法结果表
CREATE TABLE IF NOT EXISTS algorithm_results (
    id INTEGER PRIMARY KEY,
    task_id VARCHAR(50) NOT NULL,
    algorithm_type VARCHAR(50) NOT NULL, -- suspected_wash_trading, high_frequency_trading, etc.
    contract_name VARCHAR(100),
    user_id VARCHAR(50),
    risk_level VARCHAR(20), -- HIGH, MEDIUM, LOW
    confidence_score DECIMAL(5,4), -- 0.0000 - 1.0000
    trading_volume DECIMAL(20,8),
    trading_frequency INTEGER,
    time_window_start TIMESTAMP,
    time_window_end TIMESTAMP,
    indicators JSON, -- 存储算法指标
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. 对敲交易统一管理表
CREATE TABLE IF NOT EXISTS wash_trading_results (
    id INTEGER PRIMARY KEY,
    algorithm_result_id INTEGER NOT NULL,
    trading_type VARCHAR(20) NOT NULL, -- same_account, cross_account
    pair_count INTEGER DEFAULT 0, -- 交易对数量
    total_volume DECIMAL(20,8),
    avg_time_gap INTEGER, -- 平均时间间隔(秒)
    risk_score DECIMAL(5,4),
    detection_method VARCHAR(50), -- 检测方法
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. 同账户对敲详情表
CREATE TABLE IF NOT EXISTS same_account_wash_trading (
    id INTEGER PRIMARY KEY,
    wash_trading_id INTEGER NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    contract_name VARCHAR(100) NOT NULL,
    long_position_id VARCHAR(50),
    short_position_id VARCHAR(50),
    long_open_time TIMESTAMP,
    short_open_time TIMESTAMP,
    long_volume DECIMAL(20,8),
    short_volume DECIMAL(20,8),
    long_price DECIMAL(20,8),
    short_price DECIMAL(20,8),
    time_gap INTEGER, -- 开仓时间间隔(秒)
    volume_ratio DECIMAL(5,4), -- 成交量比例
    price_correlation DECIMAL(5,4), -- 价格相关性
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. 跨账户对敲详情表
CREATE TABLE IF NOT EXISTS cross_account_wash_trading (
    id INTEGER PRIMARY KEY,
    wash_trading_id INTEGER NOT NULL,
    user_a_id VARCHAR(50) NOT NULL,
    user_b_id VARCHAR(50) NOT NULL,
    contract_name VARCHAR(100) NOT NULL,
    bd_relationship VARCHAR(20), -- same_bd, different_bd
    trade_a_id VARCHAR(50),
    trade_b_id VARCHAR(50),
    trade_a_side INTEGER, -- 1=开多, 3=开空
    trade_b_side INTEGER,
    trade_a_time TIMESTAMP,
    trade_b_time TIMESTAMP,
    trade_a_volume DECIMAL(20,8),
    trade_b_volume DECIMAL(20,8),
    trade_a_price DECIMAL(20,8),
    trade_b_price DECIMAL(20,8),
    time_correlation DECIMAL(5,4), -- 时间相关性
    volume_correlation DECIMAL(5,4), -- 成交量相关性
    price_correlation DECIMAL(5,4), -- 价格相关性
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 5. 高频交易详情表
CREATE TABLE IF NOT EXISTS high_frequency_trading_details (
    id INTEGER PRIMARY KEY,
    algorithm_result_id INTEGER NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    contract_name VARCHAR(100) NOT NULL,
    trade_count INTEGER,
    avg_holding_time INTEGER, -- 平均持仓时间(秒)
    max_frequency DECIMAL(10,4), -- 最大交易频率(次/分钟)
    volume_concentration DECIMAL(5,4), -- 成交量集中度
    time_pattern VARCHAR(100), -- 时间模式
    risk_indicators JSON, -- 其他风险指标
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);


-- 7. 资金费率套利详情表
CREATE TABLE IF NOT EXISTS funding_rate_arbitrage_details (
    id INTEGER PRIMARY KEY,
    algorithm_result_id INTEGER NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    contract_name VARCHAR(100) NOT NULL,
    funding_rate DECIMAL(10,8), -- 资金费率
    position_size DECIMAL(20,8), -- 仓位大小
    holding_duration INTEGER, -- 持仓时长(秒)
    estimated_profit DECIMAL(20,8), -- 预估收益
    risk_exposure DECIMAL(5,4), -- 风险敞口
    market_conditions JSON, -- 市场条件
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- 数据迁移和兼容性视图
-- =============================================

-- 注意：contract_risk_analysis_view 视图已被移除
-- 原因：该视图未被任何代码实际使用，仅作为兼容性设计但未实际调用
-- 如需类似功能，可直接查询 algorithm_results 表并进行相应的聚合操作