"""
合约风险分析模块API
"""
from flask import Blueprint, request, jsonify
import pandas as pd
import os
import uuid
import tempfile
import json
import time
import logging
from datetime import datetime
from threading import Lock
from typing import List, Dict, Any
from werkzeug.utils import secure_filename
import numpy as np

from ..services.contract_analyzer import CTContractAnalyzer
from database.repositories.task_repository import task_repository
from database.repositories.contract_risk_repository import contract_risk_repository
from database.algorithm_storage_manager import AlgorithmStorageManager
from core.utils.decorators import login_required, admin_required
from core.security.csrf_protection import csrf_protect


# 配置日志
logger = logging.getLogger(__name__)

# 全局进度存储和锁
PROGRESS_STORE = {}
PROGRESS_LOCK = Lock()

def update_progress(task_id, progress_data):
    """更新任务进度"""
    with PROGRESS_LOCK:
        PROGRESS_STORE[task_id] = {
            'timestamp': time.time(),
            'data': progress_data
        }

def get_progress(task_id):
    """获取任务进度"""
    with PROGRESS_LOCK:
        return PROGRESS_STORE.get(task_id, {}).get('data', {})

contract_bp = Blueprint('contract', __name__)

# 允许的文件类型
ALLOWED_EXTENSIONS = {'xlsx', 'xls', 'csv'}

def allowed_file(filename):
    """检查文件是否为允许的类型"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def json_serializable(obj):
    """
    将对象转换为JSON可序列化的格式
    处理pandas Timestamp、datetime等特殊类型
    🚀 修复：处理 Infinity 和 NaN 值
    """
    import math

    # 🚀 新增：处理 Infinity 和 NaN 值
    if isinstance(obj, (int, float)):
        if math.isinf(obj):
            return 999.99 if obj > 0 else -999.99  # 用大数值替代无穷大
        elif math.isnan(obj):
            return None  # NaN 转为 null

    if hasattr(obj, 'isoformat'):  # datetime, Timestamp等
        return obj.isoformat()
    elif hasattr(obj, 'item'):  # numpy数值类型
        item_value = obj.item()
        # 递归处理 numpy 数值
        return json_serializable(item_value)
    elif isinstance(obj, set):  # set类型转换为列表
        return [json_serializable(item) for item in obj]
    elif isinstance(obj, frozenset):  # frozenset类型转换为列表
        return [json_serializable(item) for item in obj]
    elif isinstance(obj, dict):
        return {k: json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [json_serializable(item) for item in obj]
    elif pd.isna(obj):  # pandas NaN值
        return None
    else:
        return obj

def clean_results_for_json(results):
    """
    清理检测结果，确保可以JSON序列化
    """
    cleaned_results = []
    for result in results:
        cleaned_result = json_serializable(result)
        cleaned_results.append(cleaned_result)
    return cleaned_results

@admin_required  # 只有管理员可以上传文件
@csrf_protect
@contract_bp.route('/upload', methods=['POST'])
def upload_contract_data():
    """上传合约数据进行风险分析"""
    if 'file' not in request.files:
        return jsonify({'error': '未检测到文件'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': '未选择文件'}), 400
    
    if not allowed_file(file.filename):
        return jsonify({'error': '仅支持xlsx、xls、csv文件'}), 400
    
    # 检查是否使用异步模式
    use_async = request.form.get('async', 'true').lower() == 'true'

    # 检查处理模式（新增：增量处理支持）
    processing_mode = request.form.get('processing_mode', 'normal')  # 'normal' 或 'incremental'
    
    if use_async:
        # 异步模式：创建任务并返回任务ID
        task_id = str(uuid.uuid4())
        
        # 保存文件到临时位置
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1])
        file.save(temp_file.name)
        
        # 创建任务数据
        task_data = {
            'file_path': temp_file.name,
            'filename': file.filename,
            'analysis_type': request.form.get('analysis_type', 'full'),
            'enable_advanced_detection': request.form.get('enable_advanced_detection', 'true').lower() == 'true',
            'processing_mode': processing_mode  # 新增：处理模式
        }
        
        # 创建DuckDB任务，直接设置为processing状态避免竞态条件
        try:
            task_repository.create_task(task_id, 'contract_analysis', file.filename, 'processing')
            logger.info(f"✅ 任务创建成功: {task_id}")
        except Exception as create_error:
            logger.error(f"❌ 任务创建失败: {task_id}, 错误: {create_error}")
            return jsonify({'error': f'任务创建失败: {str(create_error)}'}), 500
        
        # 启动异步处理（这里可以使用线程或其他异步机制）
        import threading

        def safe_process_task():
            """安全的任务处理包装器"""
            try:
                process_contract_analysis_task(task_id, task_data)
            except Exception as thread_error:
                logger.error(f"❌ 异步任务处理异常: {task_id}, 错误: {thread_error}")
                # 确保任务状态被标记为失败
                try:
                    task_repository.update_task_status(task_id, 'failed', 0, f'任务处理异常: {str(thread_error)}')
                except Exception as status_error:
                    logger.error(f"❌ 更新失败状态也失败: {task_id}, 错误: {status_error}")

        thread = threading.Thread(target=safe_process_task)
        thread.daemon = True  # 设置为守护线程，避免阻塞程序退出
        thread.start()
        
        return jsonify({
            'task_id': task_id,
            'status': 'pending',
            'message': '合约风险分析任务已创建'
        })
    else:
        # 同步模式：保持向后兼容
        return _process_contract_sync(file)

def _process_incremental_sync(df: pd.DataFrame, analyzer: CTContractAnalyzer):
    """增量处理模式的同步分析"""
    try:
        # 🚀 修复：增量模式也需要数据标准化
        logger.info("🚀 增量模式：开始数据标准化...")
        df_standardized = analyzer._standardize_fields(df)
        logger.info(f"🚀 增量模式：数据标准化完成，字段: {list(df_standardized.columns)}")

        # 导入增量处理器
        from ..services.incremental_processor import SimpleIncrementalProcessor

        # 创建增量处理器
        processor = SimpleIncrementalProcessor(task_id=f"sync_incremental_{uuid.uuid4()}")

        # 使用增量处理器处理标准化后的数据
        complete_positions = processor.process_new_data(df_standardized)

        # 如果有完整订单，使用分析器进行风险检测
        if complete_positions:
            # 将完整订单转换为DataFrame格式供分析器使用
            position_df = processor._convert_positions_to_dataframe(complete_positions)
            # 🚀 增量模式：标记数据已预处理，跳过订单构建
            results = analyzer.process_contract_data(position_df, is_pre_processed=True)
        else:
            results = []

        logger.info(f"增量处理完成，完整订单: {len(complete_positions)}, 检测结果: {len(results)}")
        return results

    except Exception as e:
        logger.error(f"增量处理失败: {str(e)}")
        # 🚀 重构：使用统一的普通模式处理函数
        logger.info("降级到普通处理模式")
        return _process_normal_mode_unified(df, analyzer, progress_callback=None, use_waiting_table=False)

def _process_contract_sync(file):
    """同步处理合约分析（保持向后兼容）"""
    try:
        # 保存文件到临时位置
        filename = secure_filename(file.filename)
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(filename)[1])
        file.save(temp_file.name)
        
        # 读取数据
        if filename.endswith('.csv'):
            # 🚀 修复：增强CSV读取的容错能力
            try:
                df = pd.read_csv(temp_file.name, dtype={'digital_id': str, 'recommender_digital_id': str})
            except pd.errors.ParserError as e:
                logger.warning(f"CSV解析失败，尝试使用容错模式: {e}")
                try:
                    # 使用Python引擎和容错参数重试
                    df = pd.read_csv(
                        temp_file.name,
                        dtype={'digital_id': str, 'recommender_digital_id': str},
                        engine='python',  # 使用Python引擎，更容错
                        on_bad_lines='skip',  # 跳过有问题的行
                        quoting=3,  # QUOTE_NONE，不处理引号
                        encoding='utf-8'
                    )
                    logger.info(f"容错模式读取成功，数据量: {len(df)}")
                except Exception as e2:
                    logger.error(f"容错模式也失败: {e2}")
                    # 最后尝试：逐行读取并跳过错误行
                    try:
                        df = pd.read_csv(
                            temp_file.name,
                            dtype={'digital_id': str, 'recommender_digital_id': str},
                            engine='python',
                            on_bad_lines='skip',
                            encoding='utf-8',
                            sep=',',
                            quotechar='"',
                            escapechar='\\'
                        )
                        logger.info(f"最终容错模式读取成功，数据量: {len(df)}")
                    except Exception as e3:
                        raise Exception(f"CSV文件解析失败，请检查文件格式。错误详情: {str(e3)}")
        else:
            df = pd.read_excel(temp_file.name, engine='openpyxl', dtype={'digital_id': str, 'recommender_digital_id': str})
        
        # 检查处理模式（同步模式也支持增量处理）
        processing_mode = request.form.get('processing_mode', 'normal')

        # 初始化分析器
        analyzer = CTContractAnalyzer()

        # 根据处理模式执行分析
        if processing_mode == 'incremental':
            # 使用增量处理模式
            results = _process_incremental_sync(df, analyzer)
        else:
            # 使用普通模式（但仍然保存不完整订单到等待表）
            results = _process_normal_with_waiting_table(df, analyzer)
        
        # 调试信息

        
        logger.info(f"🔍 同步模式调试: 分析完成，结果数={len(results)}")
        if results:
            logger.info(f"🔍 第一个结果类型: {type(results[0])}")
            logger.info(f"🔍 第一个结果内容: {results[0]}")
        
        # 生成摘要数据
        summary = _generate_summary(results, df)
        
        # 统计合约信息
        contract_stats = []
        if 'contract_name' in df.columns:
            contract_stats = df.groupby('contract_name').size().reset_index(name='count').sort_values('count', ascending=False).to_dict(orient='records')
        
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 构建结果数据
        result_data = {
            'task_id': task_id,
            'status': 'completed',
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'filename': filename,
            'contract_risks': results,
            'summary': summary,
            'contract_stats': contract_stats,
            'total_analyzed': len(df),
            'risks_found': len(results),
            'message': f'成功分析 {len(df)} 条记录，发现 {len(results)} 个风险点'
        }
        
        # 保存到DuckDB
        task_repository.create_task(task_id, 'contract_analysis', filename)
        task_repository.update_task_status(task_id, 'completed', 100, result_data['message'])

        # 使用数据适配器保存结果（智能路由到新旧存储）
        from modules.contract_risk_analysis.services.data_adapter import ContractDataAdapter
        adapter = ContractDataAdapter()

        # 保存分析结果（会自动保存到新旧存储）
        save_success = adapter.save_analysis_result(
            task_id=task_id,
            analysis_type='full',
            filename=filename,
            total_contracts=len(df),
            risk_contracts=len(results),
            wash_trading_count=len([r for r in results if r.get('detection_type') == 'wash_trading']),
            cross_bd_count=0,  # 这里可以根据实际情况计算
            result_data=result_data
        )

        if save_success:
            logger.info(f"✅ 数据适配器保存成功")
        else:
            logger.error("❌ 数据适配器保存失败")
        
        # 返回格式与前端期望的格式一致
        return jsonify({
            'status': 'success',
            'task_id': task_id,
            'rows': len(df),
            'result': {
                'suspicious': results,
                'summary': summary
            },
            'contract_stats': contract_stats,
            'message': f'成功分析 {len(df)} 条记录，发现 {len(results)} 个风险点'
        })
        
    except Exception as e:
        return jsonify({'error': f'分析失败: {str(e)}'}), 500
    finally:
        # 清理临时文件
        if 'temp_file' in locals() and hasattr(temp_file, 'name') and os.path.exists(temp_file.name):
            os.remove(temp_file.name)

def _process_incremental_async(df: pd.DataFrame, analyzer: CTContractAnalyzer, task_id: str, progress_callback):
    """增量处理模式的异步分析"""
    try:
        # 导入增量处理器
        from ..services.incremental_processor import SimpleIncrementalProcessor

        # 更新进度：初始化增量处理器
        if progress_callback:
            progress_callback({
                'stage': '增量处理初始化',
                'percentage': 5,
                'message': '正在初始化增量处理器...'
            })

        # 创建增量处理器
        processor = SimpleIncrementalProcessor(task_id=task_id)

        # 🚀 修复：增量模式也需要数据标准化
        if progress_callback:
            progress_callback({
                'stage': '数据标准化',
                'percentage': 20,
                'message': '正在进行数据标准化...'
            })

        logger.info("🚀 增量模式：开始数据标准化...")
        df_standardized = analyzer._standardize_fields(df)
        logger.info(f"🚀 增量模式：数据标准化完成，字段: {list(df_standardized.columns)}")

        # 更新进度：处理新数据
        if progress_callback:
            progress_callback({
                'stage': '增量数据处理',
                'percentage': 30,
                'message': '正在进行增量数据处理...'
            })

        # 使用增量处理器处理标准化后的数据
        complete_positions = processor.process_new_data(df_standardized)

        # 更新进度：风险检测
        if progress_callback:
            progress_callback({
                'stage': '风险检测',
                'percentage': 70,
                'message': f'正在对 {len(complete_positions)} 个完整订单进行风险检测...'
            })

        # 如果有完整订单，使用分析器进行风险检测
        if complete_positions:
            # 将完整订单转换为DataFrame格式供分析器使用
            position_df = processor._convert_positions_to_dataframe(complete_positions)
            # 🚀 增量模式：标记数据已预处理，跳过订单构建
            results = analyzer.process_contract_data(position_df, progress_callback, is_pre_processed=True)
        else:
            results = []

        logger.info(f"增量处理完成，完整订单: {len(complete_positions)}, 检测结果: {len(results)}")
        return results

    except Exception as e:
        logger.error(f"增量处理失败: {str(e)}")
        # 降级到普通处理模式
        logger.info("降级到普通处理模式")
        if progress_callback:
            progress_callback({
                'stage': '降级处理',
                'percentage': 50,
                'message': '增量处理失败，降级到普通处理模式...'
            })
        # 🚀 重构：使用统一的普通模式处理函数
        return _process_normal_mode_unified(df, analyzer, progress_callback, use_waiting_table=False)


def _process_normal_mode_unified(df: pd.DataFrame, analyzer, progress_callback=None, use_waiting_table=True) -> List[Dict[str, Any]]:
    """
    🚀 统一的普通模式处理函数

    Args:
        df: 原始数据
        analyzer: 分析器
        progress_callback: 进度回调函数
        use_waiting_table: 是否使用等待表功能

    Returns:
        分析结果列表
    """
    try:
        if use_waiting_table:
            # 尝试使用等待表功能
            logger.info("🚀 普通模式：尝试使用等待表功能...")
            df_standardized = analyzer._standardize_fields(df)

            # 导入增量处理器
            from ..services.incremental_processor import SimpleIncrementalProcessor

            # 创建增量处理器（无task_id，表示普通模式）
            processor = SimpleIncrementalProcessor()

            # 先尝试从等待表补全现有数据
            complete_positions = processor.process_new_data(df_standardized)

            if complete_positions:
                # 将完整订单转换为DataFrame格式供分析器使用
                position_df = processor._convert_positions_to_dataframe(complete_positions)
                # 使用预处理数据进行分析
                results = analyzer.process_contract_data(position_df, progress_callback, is_pre_processed=True)
                logger.info(f"✅ 普通模式（含等待表）处理完成，完整订单: {len(complete_positions)}, 检测结果: {len(results)}")
                return results
            else:
                logger.info("⚠️ 等待表未产生完整订单，降级到纯普通模式")

        # 降级到纯普通模式或直接使用纯普通模式
        logger.info("🚀 使用纯普通模式进行分析...")
        results = analyzer.process_contract_data(df, progress_callback, is_pre_processed=False)
        logger.info(f"✅ 纯普通模式处理完成，检测结果: {len(results)}")
        return results

    except Exception as e:
        logger.warning(f"等待表功能失败，自动降级到纯普通模式: {e}")
        # 自动降级到纯普通模式
        return analyzer.process_contract_data(df, progress_callback, is_pre_processed=False)

def _process_normal_with_waiting_table(df: pd.DataFrame, analyzer) -> List[Dict[str, Any]]:
    """
    🚀 重构：使用统一的普通模式处理函数
    保持向后兼容的包装函数
    """
    return _process_normal_mode_unified(df, analyzer, progress_callback=None, use_waiting_table=True)


def _process_normal_with_waiting_table_async(df: pd.DataFrame, analyzer, task_id: str, progress_callback) -> List[Dict[str, Any]]:
    """
    🚀 重构：使用统一的普通模式处理函数（异步版本）
    保持向后兼容的包装函数
    """
    return _process_normal_mode_unified(df, analyzer, progress_callback, use_waiting_table=True)


def process_contract_analysis_task(task_id, task_data):
    """处理合约风险分析任务"""
    try:
        # 任务已经以processing状态创建，无需再次更新状态
        logger.info(f"开始处理合约分析任务: {task_id}")

        # 初始化进度
        update_progress(task_id, {
            'stage': '任务初始化',
            'percentage': 0,
            'message': '正在初始化分析任务...'
        })
        
        file_path = task_data['file_path']
        filename = task_data['filename']
        processing_mode = task_data.get('processing_mode', 'normal')  # 获取处理模式
        
        # 进度更新：读取数据
        update_progress(task_id, {
            'stage': '数据读取',
            'percentage': 5,
            'message': f'正在读取文件: {filename}...'
        })
        
        # 读取数据
        if filename.endswith('.csv'):
            # 🚀 修复：增强CSV读取的容错能力
            try:
                df = pd.read_csv(file_path, dtype={'digital_id': str, 'recommender_digital_id': str})
            except pd.errors.ParserError as e:
                logger.warning(f"CSV解析失败，尝试使用容错模式: {e}")
                try:
                    # 使用Python引擎和容错参数重试
                    df = pd.read_csv(
                        file_path,
                        dtype={'digital_id': str, 'recommender_digital_id': str},
                        engine='python',  # 使用Python引擎，更容错
                        on_bad_lines='skip',  # 跳过有问题的行
                        quoting=3,  # QUOTE_NONE，不处理引号
                        encoding='utf-8'
                    )
                    logger.info(f"容错模式读取成功，数据量: {len(df)}")
                except Exception as e2:
                    logger.error(f"容错模式也失败: {e2}")
                    # 最后尝试：逐行读取并跳过错误行
                    try:
                        df = pd.read_csv(
                            file_path,
                            dtype={'digital_id': str, 'recommender_digital_id': str},
                            engine='python',
                            on_bad_lines='skip',
                            encoding='utf-8',
                            sep=',',
                            quotechar='"',
                            escapechar='\\'
                        )
                        logger.info(f"最终容错模式读取成功，数据量: {len(df)}")
                    except Exception as e3:
                        raise Exception(f"CSV文件解析失败，请检查文件格式。错误详情: {str(e3)}")
        else:
            df = pd.read_excel(file_path, engine='openpyxl', dtype={'digital_id': str, 'recommender_digital_id': str})
        
        # 进度更新：数据读取完成
        update_progress(task_id, {
            'stage': '数据读取完成',
            'percentage': 10,
            'message': f'成功读取 {len(df)} 条记录，正在初始化分析器...'
        })
        
        # 初始化分析器
        analyzer = CTContractAnalyzer(task_id=task_id)
        
        # 创建进度回调函数
        def progress_callback(progress_info):
            # 将分析器的进度映射到总进度的10%-95%范围
            analyzer_percentage = progress_info.get('percentage', 0)
            total_percentage = 10 + (analyzer_percentage / 100) * 85
            
            update_progress(task_id, {
                'stage': progress_info.get('stage', '正在分析'),
                'percentage': min(total_percentage, 95),
                'message': progress_info.get('message', '正在进行合约风险分析...'),
                'details': progress_info
            })
        
        # 根据处理模式执行分析
        if processing_mode == 'incremental':
            # 使用增量处理模式
            analysis_result = _process_incremental_async(df, analyzer, task_id, progress_callback)
        else:
            # 使用普通模式（但仍然保存不完整订单到等待表）
            analysis_result = _process_normal_with_waiting_table_async(df, analyzer, task_id, progress_callback)

        # 提取结果数据
        if isinstance(analysis_result, dict):
            results = analysis_result.get('results', [])
        else:
            # 向后兼容：如果返回的是列表格式
            results = analysis_result

        # 进度更新：结果处理
        update_progress(task_id, {
            'stage': '结果处理',
            'percentage': 95,
            'message': f'正在处理 {len(results)} 个检测结果...'
        })

        # 生成摘要数据
        summary = _generate_summary(results, df)
        
        # 统计合约信息
        contract_stats = []
        if 'contract_name' in df.columns:
            contract_stats = df.groupby('contract_name').size().reset_index(name='count').sort_values('count', ascending=False).to_dict(orient='records')
        
        # 清理结果数据，确保JSON可序列化
        cleaned_results = clean_results_for_json(results)
        cleaned_summary = json_serializable(summary)
        cleaned_contract_stats = json_serializable(contract_stats)
        
        # 构建最终结果
        result_data = {
            'task_id': task_id,
            'status': 'completed',
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'filename': filename,
            'analysis_type': task_data.get('analysis_type', 'full'),
            'contract_risks': cleaned_results,
            'summary': cleaned_summary,
            'contract_stats': cleaned_contract_stats,
            'total_analyzed': len(df),
            'risks_found': len(results),
            'message': f'成功分析 {len(df)} 条记录，发现 {len(results)} 个风险点'
        }
        
        # 最终进度更新
        update_progress(task_id, {
            'stage': '分析完成',
            'percentage': 100,
            'message': f'合约风险分析完成，发现 {len(results)} 个风险点',
            'final_results': {
                'total_analyzed': len(df),
                'risks_found': len(results),
                'completion_time': datetime.now().isoformat()
            }
        })
        
        # 使用数据适配器保存结果（智能路由到新旧存储）
        from modules.contract_risk_analysis.services.data_adapter import ContractDataAdapter
        adapter = ContractDataAdapter()

        # 保存分析结果（会自动保存到新旧存储）
        analysis_type = task_data.get('analysis_type', 'full')
        save_success = adapter.save_analysis_result(
            task_id=task_id,
            analysis_type=analysis_type,
            filename=filename,
            total_contracts=len(df),
            risk_contracts=len(results),
            wash_trading_count=len([r for r in results if r.get('detection_type') == 'wash_trading']),
            cross_bd_count=0,  # 这里可以根据实际情况计算
            result_data=result_data
        )

        if save_success:
            logger.info(f"✅ 数据适配器保存成功")
        else:
            logger.error("❌ 数据适配器保存失败")

        # 🚀 优化：移除批量用户行为分析，改为按需计算
        # 原来的批量预计算逻辑已被懒加载策略替代
        # 用户查询时会自动进行实时计算并保存结果
        logger.info("✅ 合约分析完成，用户行为分析已改为按需计算模式")


        
        # 完成任务 - 使用修复后的update_task_status方法
        try:
            task_repository.update_task_status(task_id, 'completed', 100, f'成功分析 {len(df)} 条记录，发现 {len(results)} 个风险点')
            logger.info(f"✅ 任务状态更新成功: {task_id} -> completed")

            # 验证任务是否真的存在于数据库中
            verify_task = task_repository.get_task(task_id)
            if verify_task and verify_task.get('status') == 'completed':
                logger.info(f"✅ 任务验证成功: {task_id} 状态为 {verify_task.get('status')}")
            else:
                logger.error(f"❌ 任务验证失败: {task_id} 状态异常")

        except Exception as update_error:
            logger.error(f"❌ 任务状态更新失败: {task_id}, 错误: {update_error}")
            # 不抛出异常，避免影响分析结果的返回
        
    except Exception as e:
        error_msg = f"合约风险分析任务失败: {str(e)}"
        
        # 错误进度更新
        update_progress(task_id, {
            'stage': '分析失败',
            'percentage': 0,
            'message': error_msg,
            'error': str(e)
        })
        
        # 标记任务失败
        task_repository.update_task_status(task_id, 'failed', 0, error_msg)
    finally:
        # 清理临时文件
        if 'file_path' in task_data and os.path.exists(task_data['file_path']):
            os.remove(task_data['file_path'])
        
        # 清理进度信息（可选，或者保留一段时间）
        # 这里我们保留进度信息，以便前端获取最终状态

def _generate_summary(results, df):
    """生成摘要数据"""
    summary = {
        'total_analyzed': len(df),
        'risks_found': len(results),
        'risk_distribution': {'high': 0, 'medium': 0, 'low': 0},
        'contract_distribution': {},
        'anomaly_distribution': {}
    }
    
    # 统计风险分布、合约分布和异常类型分布
    for result in results:
        # 风险等级分布
        risk_level = result.get('severity', result.get('risk_level', '中'))
        risk_key = 'medium'  # 默认中风险
        
        if str(risk_level).lower() in ['高', 'high', 'critical', '极高风险']:
            risk_key = 'high'
        elif str(risk_level).lower() in ['低', 'low']:
            risk_key = 'low'
        elif str(risk_level).lower() in ['中', 'medium', 'normal']:
            risk_key = 'medium'
        
        if risk_key in summary['risk_distribution']:
            summary['risk_distribution'][risk_key] += 1
        
        # 合约分布
        contract_name = result.get('contractName', result.get('contract_name', '未知合约'))
        if contract_name:
            summary['contract_distribution'][contract_name] = summary['contract_distribution'].get(contract_name, 0) + 1
        
        # 异常类型分布
        anomaly_type = result.get('detection_type', '未知类型')
        if anomaly_type:
            summary['anomaly_distribution'][anomaly_type] = summary['anomaly_distribution'].get(anomaly_type, 0) + 1
    
    return summary

@login_required  # 需要登录才能分析数据
@csrf_protect
@contract_bp.route('/analyze', methods=['POST'])
def analyze_contract_data():
    """对已有数据进行合约风险分析"""
    data = request.get_json()
    
    if not data:
        return jsonify({'error': '缺少数据'}), 400
    
    try:
        # 将JSON数据转换为DataFrame
        df = pd.DataFrame(data)
        
        # 初始化分析器（生成临时task_id用于即时分析）
        import uuid
        temp_task_id = f"temp_{str(uuid.uuid4())[:8]}"
        analyzer = CTContractAnalyzer(task_id=temp_task_id)
        
        # 🚀 重构：使用统一的普通模式处理函数
        results = _process_normal_mode_unified(df, analyzer, progress_callback=None, use_waiting_table=True)
        
        # 生成摘要数据
        summary = _generate_summary(results, df)
        
        # 清理结果数据，确保JSON可序列化
        cleaned_results = clean_results_for_json(results)
        cleaned_summary = json_serializable(summary)
        
        return jsonify({
            'status': 'success',
            'result': {
                'suspicious': cleaned_results,
                'summary': cleaned_summary
            },
            'message': f'分析完成，发现 {len(results)} 个风险点'
        })
        
    except Exception as e:
        return jsonify({'error': f'分析失败: {str(e)}'}), 500



@contract_bp.route('/status/<task_id>', methods=['GET'])
@login_required  # 需要登录才能查看任务状态
def get_task_status(task_id):
    """获取任务状态"""
    try:
        task = task_repository.get_task(task_id)
        if task:
            return jsonify({
                'task_id': task_id,
                'status': task.get('status'),
                'message': task.get('message', ''),
                'progress': task.get('progress', 0),
                'created_at': task.get('created_at'),
                'updated_at': task.get('updated_at')
            })
        else:
            return jsonify({'error': '任务不存在'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@contract_bp.route('/result/<task_id>', methods=['GET'])
@login_required  # 需要登录才能查看结果
def get_task_result(task_id):
    """获取任务分析结果 - 升级版（使用数据适配器）"""
    try:
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 50))
        
        # 从DuckDB获取任务
        task = task_repository.get_task(task_id)
        if not task:
            return jsonify({'error': '任务不存在'}), 404

        # 使用数据适配器获取分析结果（自动路由到新存储系统）
        from modules.contract_risk_analysis.services.data_adapter import ContractDataAdapter
        adapter = ContractDataAdapter()
        
        # 确保新存储系统已启用
        adapter.enable_new_storage()
        
        # 获取分析结果
        analysis_result = adapter.get_analysis_result(task_id)
        if not analysis_result:
            return jsonify({'error': '分析结果不存在'}), 404
        
        result_data = analysis_result.get('result_data', {})
        contract_risks = result_data.get('contract_risks', [])
        summary = result_data.get('summary', {})
        
        # 分页处理
        total_records = len(contract_risks)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paged_risks = contract_risks[start_idx:end_idx]
        total_pages = (total_records + page_size - 1) // page_size
        
        # 计算合约分布统计
        contract_stats = {}
        if contract_risks:
            for risk in contract_risks:
                contract_name = risk.get('contract_name') or risk.get('contractName', 'unknown')
                contract_stats[contract_name] = contract_stats.get(contract_name, 0) + 1
            # 按数量排序，取前10
            contract_stats = dict(sorted(contract_stats.items(), key=lambda x: x[1], reverse=True)[:10])
        
        # 异常类型中文映射
        anomaly_type_mapping = {
            'same_account_wash_trading': '同账户对敲',
            'cross_account_wash_trading': '跨账户对敲', 
            'suspected_wash_trading': '对敲交易',
            'high_frequency_trading': '高频交易',
            'regular_brush_trading': '规律性刷量',
            'funding_rate_arbitrage': '资金费率套利',
            'unknown': '未知类型'
        }
        
        # 数据质量统计
        quality_stats = {
            'high_quality': 0,
            'medium_quality': 0,
            'low_quality': 0,
            'total_validated': 0,
            'average_quality_score': 0.0
        }
        
        # 计算详细的异常分布（包括对敲交易细分）和数据质量统计
        detailed_anomaly_distribution = {}
        quality_scores = []
        
        if contract_risks:
            for risk in contract_risks:
                detection_type = risk.get('detection_type', 'unknown')
                detection_method = risk.get('detection_method', '')
                
                # 对敲交易需要细分
                if 'wash_trading' in detection_type:
                    if 'same_account' in detection_method:
                        key = 'same_account_wash_trading'  # 同账户对敲
                    elif 'cross_account' in detection_method:
                        key = 'cross_account_wash_trading'  # 跨账户对敲
                    else:
                        key = 'suspected_wash_trading'  # 未分类的对敲
                else:
                    key = detection_type  # 其他检测类型直接使用
                
                # 转换为中文名称
                chinese_key = anomaly_type_mapping.get(key, key)
                detailed_anomaly_distribution[chinese_key] = detailed_anomaly_distribution.get(chinese_key, 0) + 1
                
                # 统计数据质量
                data_quality = risk.get('data_quality', {})
                if data_quality:
                    quality_stats['total_validated'] += 1
                    confidence_level = data_quality.get('confidence_level', 'low')
                    quality_score = data_quality.get('quality_score', 0.0)
                    quality_scores.append(quality_score)
                    
                    if confidence_level == 'high':
                        quality_stats['high_quality'] += 1
                    elif confidence_level == 'medium':
                        quality_stats['medium_quality'] += 1
                    else:
                        quality_stats['low_quality'] += 1
        
        # 计算平均质量评分
        if quality_scores:
            quality_stats['average_quality_score'] = sum(quality_scores) / len(quality_scores)
        
        # 构建返回格式
        result = {
            'task_id': task_id,
            'task_type': 'contract_analysis',
            'status': task.get('status'),
            'message': task.get('message', ''),
            'created_at': task.get('created_at'),
            'updated_at': task.get('updated_at'),
            'result': {
                'suspicious': paged_risks,
                'summary': {
                    'risk_distribution': summary.get('risk_distribution', {}),
                    'anomaly_distribution': detailed_anomaly_distribution,  # 使用详细的异常分布
                    'quality_stats': quality_stats  # 添加数据质量统计
                },
                'contract_stats': contract_stats,  # 使用动态计算的合约统计
                'total_analyzed': result_data.get('total_analyzed', total_records),
                'risks_found': result_data.get('risks_found', total_records)
            },
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_records': total_records,
                'total_pages': total_pages,
                'has_next': page < total_pages,
                'has_prev': page > 1
            },
            'error': None
        }
        
        
        
        return jsonify(result)
    except Exception as e:
        logger.error(f"接口异常: {str(e)}", exc_info=True)
        return jsonify({'error': str(e)}), 500

@contract_bp.route('/config', methods=['GET', 'POST'])
@login_required
def handle_config():
    """处理分析配置"""
    if request.method == 'GET':
        # 返回默认配置
        default_config = {
            'algorithms': {
                'high_frequency_trading': {'enabled': True, 'threshold': 0.8},
                'wash_trading': {'enabled': True, 'threshold': 0.7},
                'funding_arbitrage': {'enabled': True, 'threshold': 0.6},
                'regular_brush': {'enabled': True, 'threshold': 0.7},

            },
            'detection_sensitivity': 'medium',
            'max_analysis_records': 100000
        }
        return jsonify(default_config)
    
    elif request.method == 'POST':
        # 更新配置
        config_data = request.get_json()
        # 配置保存功能暂未实现
        return jsonify({'message': '配置已更新', 'config': config_data})

@contract_bp.route('/data/<task_id>', methods=['GET'])
@login_required  # 需要登录才能获取合约风险数据
def get_contract_risk_data(task_id):
    """获取合约风险数据（用于合约整合分析）- 升级版"""
    try:
        # 🚀 修改：使用数据适配器智能路由到新存储
        from modules.contract_risk_analysis.services.data_adapter import ContractDataAdapter
        adapter = ContractDataAdapter()
        analysis_result = adapter.get_analysis_result(task_id)
        
        if not analysis_result:
            return jsonify({'error': '分析结果不存在'}), 404
        
        result_data = analysis_result.get('result_data', {})
        
        # 获取修复后的合约风险数据（repository已自动修复格式）
        contract_risks = result_data.get('contract_risks', [])
        
        # 构建合约整合所需的数据格式
        response_data = {
            'status': 'success',
            'task_id': task_id,
            'contract_risks': contract_risks,  # 已修复格式
            'summary': result_data.get('summary', {}),
            'total_analyzed': analysis_result.get('total_contracts', 0),
            'risks_found': analysis_result.get('risk_contracts', 0),
            'created_at': analysis_result.get('created_at'),
            'filename': analysis_result.get('filename', '')
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        return jsonify({'error': f'获取数据失败: {str(e)}'}), 500


@login_required
@csrf_protect
@contract_bp.route('/batch-query', methods=['POST'])
def batch_query():
    """批量查询用户合约异常金额接口"""
    try:
        from ..services.batch_query_service import contract_batch_query_service

        data = request.get_json()
        if not data:
            return jsonify({'error': '没有提供数据'}), 400

        member_ids = data.get('member_ids', [])
        if not member_ids:
            return jsonify({'error': '请提供member_ids列表'}), 400

        # 如果是字符串，按换行符分割
        if isinstance(member_ids, str):
            member_ids = [mid.strip() for mid in member_ids.split('\n') if mid.strip()]

        # 调用批量查询服务
        result = contract_batch_query_service.create_batch_query_task(member_ids)

        return jsonify(result)

    except Exception as e:
        logger.error(f"合约批量查询失败: {str(e)}")
        return jsonify({'error': f'批量查询失败: {str(e)}'}), 500


@contract_bp.route('/batch-query-download/<filename>', methods=['GET'])
@login_required
def batch_query_download(filename):
    """下载批量查询结果"""
    try:
        from flask import send_file
        from datetime import datetime
        import os

        # 安全检查：只允许下载临时文件
        if not filename.endswith('.xlsx'):
            return jsonify({'error': '无效的文件格式'}), 400

        # 构建文件路径（假设文件在临时目录）
        import tempfile
        file_path = os.path.join(tempfile.gettempdir(), filename)

        if not os.path.exists(file_path):
            return jsonify({'error': '文件不存在'}), 404

        # 生成下载文件名
        download_filename = f"contract_batch_query_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        return send_file(
            file_path,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=download_filename
        )

    except Exception as e:
        logger.error(f"下载批量查询结果失败: {str(e)}")
        return jsonify({'error': f'下载失败: {str(e)}'}), 500

@contract_bp.route('/tasks', methods=['GET'])
@login_required  # 需要登录才能查看任务列表
def get_completed_tasks():
    """获取已完成的合约风险分析任务列表"""
    try:
        # 从DuckDB获取合约分析任务
        tasks = task_repository.get_tasks_by_type('contract_analysis')
        
        completed_tasks = []
        
        # 🚀 修改：使用数据适配器
        from modules.contract_risk_analysis.services.data_adapter import ContractDataAdapter
        adapter = ContractDataAdapter()
        
        for task in tasks:
            if task.get('status') == 'completed':
                # 获取分析结果的详细信息
                analysis_result = adapter.get_analysis_result(task.get('task_id'))
                
                # 从数据库记录中直接获取字段，而不是从result_data中获取
                total_contracts = analysis_result.get('total_contracts', 0) if analysis_result else 0
                risk_contracts = analysis_result.get('risk_contracts', 0) if analysis_result else 0
                
                completed_tasks.append({
                    'task_id': task.get('task_id'),
                    'filename': task.get('filename', '未知文件'),
                    'total_contracts': total_contracts,
                    'risk_contracts': risk_contracts,
                    'total_analyzed': total_contracts,  # 保持向后兼容
                    'risks_found': risk_contracts,  # 保持向后兼容
                    'created_at': task.get('created_at'),
                    'status': task.get('status')
                })
        
        return jsonify({
            'status': 'success',
            'tasks': completed_tasks,
            'total': len(completed_tasks)
        })
        
    except Exception as e:
        return jsonify({'error': f'获取任务列表失败: {str(e)}'}), 500

@contract_bp.route('/progress/<task_id>', methods=['GET'])
@login_required  # 需要登录才能查看分析进度
def get_analysis_progress(task_id):
    """获取合约风险分析进度"""
    try:
        progress = get_progress(task_id)
        if not progress:
            return jsonify({'error': '任务不存在或已完成'}), 404
        
        return jsonify(progress)
    
    except Exception as e:
        return jsonify({'error': f'获取进度失败: {str(e)}'}), 500

@contract_bp.route('/health-check', methods=['POST'])
@login_required  # 需要登录才能执行健康检查
def run_task_health_check():
    """运行任务健康检查和修复"""
    try:
        from modules.contract_risk_analysis.services.task_health_monitor import task_health_monitor

        auto_fix = request.json.get('auto_fix', True) if request.is_json else True

        logger.info("手动触发任务健康检查...")
        results = task_health_monitor.run_health_check(auto_fix=auto_fix)

        return jsonify({
            'status': 'success',
            'message': '健康检查完成',
            'results': results
        })

    except Exception as e:
        logger.error(f"健康检查API失败: {e}")
        return jsonify({'error': f'健康检查失败: {str(e)}'}), 500



